/* Base Styles */
:root {
  --primary-color: #0056b3;
  --secondary-color: #003366;
  --accent-color: #007bff;
  --green-primary: #2d5a27;
  --green-secondary: #1e3a1a;
  --green-accent: #4a7c59;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --text-color: #212529;
  --border-color: #dee2e6;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --font-family: "Arial", "Helvetica Neue", sans-serif;
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--white);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.btn {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.btn:hover {
  background-color: var(--secondary-color);
  color: var(--white);
  text-decoration: none;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--accent-color);
}

/* Header Styles */
.header {
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
  text-decoration: none;
}

.main-nav ul {
  display: flex;
}

.main-nav ul li {
  margin-left: 30px;
}

.main-nav ul li a {
  color: var(--text-color);
  font-weight: 600;
  position: relative;
}

.main-nav ul li a:hover,
.main-nav ul li a.active {
  color: var(--primary-color);
}

.main-nav ul li a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.main-nav ul li a:hover::after,
.main-nav ul li a.active::after {
  width: 100%;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--text-color);
  margin: 2px 0;
  transition: var(--transition);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--green-primary) 0%, var(--green-secondary) 50%, var(--green-accent) 100%);
  background-size: cover;
  background-position: center;
  color: var(--white);
  padding: 100px 0;
  text-align: center;
  position: relative;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(45, 90, 39, 0.1) 0%, rgba(74, 124, 89, 0.1) 100%);
  pointer-events: none;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 2.8rem;
  margin-bottom: 20px;
}

.hero p {
  font-size: 1.1rem;
  margin-bottom: 20px;
  opacity: 0.9;
}

.hero .btn {
  margin-top: 20px;
  font-size: 1.1rem;
  padding: 12px 30px;
}

/* About Us Section */
.about-us {
  padding: 80px 0;
  background-color: var(--light-color);
}

.about-content {
  max-width: 900px;
  margin: 0 auto;
}

.about-text {
  flex: 1;
}

.about-text p {
  margin-bottom: 20px;
  font-size: 1.05rem;
}



/* Game Categories Section */
.game-categories {
  padding: 80px 0;
}

.categories-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.category-item {
  background-color: var(--light-color);
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
}

.category-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.category-item h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.category-item ul li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 20px;
}

.category-item ul li::before {
  content: "•";
  color: var(--accent-color);
  position: absolute;
  left: 0;
  top: 0;
}



/* Gaming Conditions Section */
.gaming-conditions {
  padding: 80px 0;
  background-color: var(--light-color);
}

.conditions-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.condition-item {
  background-color: var(--white);
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
}

.condition-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.condition-item h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.condition-item p,
.condition-item ul li {
  margin-bottom: 10px;
}

.condition-item ul li {
  position: relative;
  padding-left: 20px;
}

.condition-item ul li::before {
  content: "•";
  color: var(--accent-color);
  position: absolute;
  left: 0;
  top: 0;
}



/* Bonuses Section */
.bonuses {
  padding: 80px 0;
}

.bonuses-content {
  max-width: 900px;
  margin: 0 auto;
}

.bonuses-content > p {
  text-align: center;
  font-size: 1.1rem;
  margin-bottom: 40px;
}

.bonus-item {
  background-color: var(--light-color);
  padding: 30px;
  border-radius: 8px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
}

.bonus-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.bonus-item h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.bonus-item p {
  margin-bottom: 15px;
}

.bonus-item ol {
  padding-left: 20px;
  margin-bottom: 15px;
}

.bonus-item ol li {
  margin-bottom: 10px;
}

.bonus-disclaimer {
  background-color: var(--gray-200);
  padding: 15px;
  border-radius: 8px;
  font-size: 0.9rem;
  color: var(--gray-700);
  margin-top: 30px;
}

/* Advantages Section */
.advantages {
  padding: 80px 0;
  background-color: var(--light-color);
}

.advantages-content {
  max-width: 1000px;
  margin: 0 auto;
}

.advantages-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
}

.advantage-item {
  background-color: var(--white);
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
}

.advantage-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.advantage-item h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-size: 1.3rem;
}



/* Responsible Gaming Section */
.responsible-gaming {
  padding: 80px 0;
}

.responsible-content {
  max-width: 900px;
  margin: 0 auto;
}

.responsible-content > p {
  text-align: center;
  font-size: 1.1rem;
  margin-bottom: 40px;
}

.responsible-item {
  background-color: var(--light-color);
  padding: 30px;
  border-radius: 8px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.responsible-item h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.responsible-item p {
  margin-bottom: 15px;
}

.responsible-item ul li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 20px;
}

.responsible-item ul li::before {
  content: "•";
  color: var(--accent-color);
  position: absolute;
  left: 0;
  top: 0;
}

.responsible-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

/* Support and Regulation Section */
.support-regulation {
  padding: 80px 0;
  background-color: var(--light-color);
}

.support-content {
  max-width: 900px;
  margin: 0 auto;
}

.support-item {
  background-color: var(--white);
  padding: 25px;
  border-radius: 8px;
  margin-bottom: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.support-item h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.support-item p {
  margin-bottom: 15px;
}

.support-item ul li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 20px;
}

.support-item ul li::before {
  content: "•";
  color: var(--accent-color);
  position: absolute;
  left: 0;
  top: 0;
}

/* Footer */
.footer {
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 60px 0 30px;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 40px;
}

.footer-logo .logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--white);
  margin-bottom: 20px;
  display: block;
}

.footer-links {
  display: flex;
  justify-content: space-between;
}

.footer-links-column {
  flex: 1;
}

.footer-links-column h4 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: var(--white);
  position: relative;
  padding-bottom: 10px;
}

.footer-links-column h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--accent-color);
}

.footer-links-column ul li {
  margin-bottom: 10px;
}

.footer-links-column ul li a {
  color: var(--gray-300);
  transition: var(--transition);
}

.footer-links-column ul li a:hover {
  color: var(--white);
  text-decoration: none;
  padding-left: 5px;
}

.footer-legal {
  text-align: right;
}

.legal-links {
  margin: 15px 0;
}

.legal-links a {
  color: var(--gray-300);
  margin-left: 15px;
  font-size: 0.9rem;
}

.legal-links a:hover {
  color: var(--white);
}

.age-restriction {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.age-18 {
  display: inline-block;
  width: 40px;
  height: 40px;
  background-color: var(--danger-color);
  color: var(--white);
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  font-weight: bold;
  margin-right: 10px;
}

.age-restriction p {
  font-size: 0.9rem;
  color: var(--gray-300);
}

/* Legal Pages */
.legal-page {
  padding: 80px 0;
  background-color: var(--white);
}

.legal-content {
  max-width: 900px;
  margin: 0 auto;
}

.legal-section {
  margin-bottom: 40px;
}

.legal-section h2 {
  color: var(--primary-color);
  font-size: 1.8rem;
  margin-bottom: 20px;
  border-bottom: 2px solid var(--accent-color);
  padding-bottom: 10px;
}

.legal-section h3 {
  color: var(--secondary-color);
  font-size: 1.4rem;
  margin-bottom: 15px;
  margin-top: 25px;
}

.legal-section p {
  margin-bottom: 15px;
  line-height: 1.7;
}

.legal-section ul {
  margin-bottom: 15px;
  padding-left: 20px;
}

.legal-section ul li {
  list-style: disc;
  margin-bottom: 8px;
  line-height: 1.6;
}

.legal-disclaimer {
  background-color: var(--light-color);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  margin-top: 40px;
}

.legal-disclaimer p {
  font-style: italic;
  color: var(--gray-600);
  margin: 0;
}

/* Cookie Table Styles */
.cookie-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  background-color: var(--white);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cookie-table th,
.cookie-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.cookie-table th {
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.cookie-table tr:hover {
  background-color: var(--gray-100);
}

.cookie-table tr:last-child td {
  border-bottom: none;
}

.cookie-table td:first-child {
  font-weight: 600;
  color: var(--primary-color);
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  z-index: 999;
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: var(--secondary-color);
}

.arrow-up {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 12px solid var(--white);
}

/* Safe Gaming Page Styles */
.safe-gaming-page {
  padding: 80px 0;
}

.page-header {
  text-align: center;
  margin-bottom: 50px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.safe-gaming-content {
  max-width: 900px;
  margin: 0 auto;
}

.safe-gaming-content > p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.safe-gaming-section {
  margin-bottom: 40px;
}

.safe-gaming-section h2 {
  color: var(--primary-color);
  font-size: 1.8rem;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--accent-color);
}

.safe-gaming-section h3 {
  color: var(--secondary-color);
  font-size: 1.4rem;
  margin: 25px 0 15px;
}

.safe-gaming-section p {
  margin-bottom: 15px;
}

.safe-gaming-section ul {
  margin-bottom: 20px;
}

.safe-gaming-section ul li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 20px;
}

.safe-gaming-section ul li::before {
  content: "•";
  color: var(--accent-color);
  position: absolute;
  left: 0;
  top: 0;
}

.table-responsive {
  overflow-x: auto;
  margin-bottom: 30px;
}

.regional-support-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.regional-support-table th,
.regional-support-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.regional-support-table th {
  background-color: var(--primary-color);
  color: var(--white);
}

.regional-support-table tr:nth-child(even) {
  background-color: var(--gray-100);
}

.regional-support-table tr:hover {
  background-color: var(--gray-200);
}

.support-links {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px;
  margin: 40px 0;
}

.support-links a {
  display: block;
  padding: 10px 15px;
  background-color: var(--primary-color);
  color: var(--white);
  text-decoration: none;
  border-radius: 5px;
  font-weight: 500;
  transition: var(--transition);
}

.support-links a:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
}

.age-restriction-notice {
  background-color: var(--gray-200);
  padding: 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;
}

.age-restriction-notice .age-18 {
  margin-right: 15px;
}

.age-restriction-notice p {
  font-weight: 600;
  margin-bottom: 0;
}
