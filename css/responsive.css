/* Responsive Styles */

/* Large devices (desktops, less than 1200px) */
@media (max-width: 1199.98px) {
  .container {
    max-width: 960px;
  }

  .section-header h2 {
    font-size: 2.2rem;
  }

  .hero h1 {
    font-size: 2.5rem;
  }



  .footer-content {
    grid-template-columns: 1fr 1fr;
  }

  .footer-legal {
    grid-column: span 2;
    text-align: center;
    margin-top: 30px;
  }

  .legal-links {
    justify-content: center;
  }

  .age-restriction {
    justify-content: center;
  }

  .regulator-links {
    gap: 20px;
  }

  .regulator-links img {
    height: 35px;
  }
}

/* Medium devices (tablets, less than 992px) */
@media (max-width: 991.98px) {
  .container {
    max-width: 720px;
  }



  .categories-content {
    grid-template-columns: 1fr;
  }



  .conditions-content {
    grid-template-columns: 1fr;
  }



  .advantages-list {
    grid-template-columns: 1fr;
  }

  .responsible-links {
    flex-direction: column;
    align-items: center;
  }

  .responsible-links .btn {
    width: 100%;
    max-width: 300px;
    margin-bottom: 15px;
  }
}

/* Small devices (landscape phones, less than 768px) */
@media (max-width: 767.98px) {
  .container {
    max-width: 540px;
  }

  .header-content {
    position: relative;
  }

  .main-nav {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--white);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: none;
  }

  .main-nav.active {
    display: block;
  }

  .main-nav ul {
    flex-direction: column;
  }

  .main-nav ul li {
    margin: 0 0 15px 0;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .hero {
    padding: 60px 0;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .section-header h2 {
    font-size: 1.8rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-logo {
    text-align: center;
  }

  .footer-links {
    flex-direction: column;
    gap: 30px;
  }

  .footer-legal {
    grid-column: 1;
    text-align: center;
  }

  .support-logos {
    gap: 20px;
  }

  .support-logos a {
    max-width: 120px;
  }
}

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
  .container {
    padding: 0 20px;
  }

  .hero h1 {
    font-size: 1.8rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .section-header h2 {
    font-size: 1.6rem;
  }

  .bonus-item,
  .responsible-item,
  .support-item,
  .condition-item,
  .advantage-item {
    padding: 20px;
  }

  .bonus-item h3,
  .responsible-item h3,
  .support-item h3,
  .condition-item h3,
  .advantage-item h3 {
    font-size: 1.3rem;
  }

  .page-header h1 {
    font-size: 1.8rem;
  }

  .safe-gaming-section h2 {
    font-size: 1.5rem;
  }

  .safe-gaming-section h3 {
    font-size: 1.3rem;
  }

  .regional-support-table th,
  .regional-support-table td {
    padding: 8px 10px;
    font-size: 0.9rem;
  }

  .back-to-top {
    width: 40px;
    height: 40px;
    bottom: 20px;
    right: 20px;
  }

  .regulator-links {
    gap: 15px;
    justify-content: center;
  }

  .regulator-links img {
    height: 30px;
    max-width: 80px;
  }

  .regulator-logos h4 {
    font-size: 1rem;
    margin-bottom: 15px;
  }
}
